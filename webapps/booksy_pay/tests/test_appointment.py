from decimal import Decimal
from unittest.mock import PropertyMock

import pytest
from django.test import override_settings
from mock import (
    MagicMock,
    patch,
)

from model_bakery import baker

from country_config import Country
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayAvailabilityOnBookingFlag,
    BooksyPaySeparateSettingsFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import Appointment, ServiceVariant
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import service_variant_recipe
from webapps.business.enums import PriceType
from webapps.business.models import ServiceVariantPayment
from webapps.business.service_price import ServicePrice
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.family_and_friends.tests.utils import create_appointment_for_member
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import Transaction
from webapps.pos.baker_recipes import (
    payment_type_recipe,
    pos_recipe,
    receipt_recipe,
    transaction_recipe,
)
from webapps.pos.enums import receipt_status
from webapps.pos.utils import is_call_for_status


@pytest.mark.parametrize(
    'appointment_status, expected',
    [
        (Appointment.STATUS.ACCEPTED, True),
        (Appointment.STATUS.FINISHED, True),
        (Appointment.STATUS.CANCELED, False),
    ],
)
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@patch('webapps.booksy_pay.utils.is_no_show_protection_active', return_value=False)
@pytest.mark.django_db
def test_is_booksy_pay_available(
    is_no_show_protection_active_mock,
    appointment_status,
    expected,
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=appointment_status,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
    )

    assert appointment.is_booksy_pay_available is expected
    if appointment_status in (Appointment.STATUS.ACCEPTED, Appointment.STATUS.FINISHED):
        is_no_show_protection_active_mock.assert_called_once()
    else:
        is_no_show_protection_active_mock.assert_not_called()


@pytest.mark.parametrize(
    'prepaid_value, expected',
    [
        (False, True),
        (True, False),
    ],
)
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@patch('webapps.booking.models.Appointment.is_prepaid', new_callable=PropertyMock)
@patch('webapps.pos.utils.is_no_show_protection_active', return_value=False)
@pytest.mark.django_db
def test_is_booksy_pay_available_for_prepaid_appointment(
    is_no_show_protection_active_mock,
    is_prepaid_mock,
    prepaid_value,
    expected,
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
    )

    is_prepaid_mock.return_value = prepaid_value
    assert appointment.is_booksy_pay_available is expected


@pytest.mark.parametrize(
    'total_type, expected',
    [
        (PriceType.DONT_SHOW, False),
        (PriceType.FIXED, True),
    ],
)
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@pytest.mark.django_db
def test_is_booksy_pay_available_for_fixed_price_type_only(
    total_type,
    expected,
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=Appointment.STATUS.ACCEPTED,
        total_type=total_type,
        total_value=Decimal('123.00'),
    )
    assert appointment.is_booksy_pay_available is expected


@pytest.mark.parametrize(
    'country, total_value, expected',
    [
        (Country.US, None, False),
        (Country.US, Decimal('0.50'), True),
        (Country.US, Decimal('0.49'), False),
        (Country.PL, Decimal('10.00'), True),
        (Country.PL, Decimal('9.99'), False),
        (Country.ZA, Decimal('35.00'), True),
        (Country.ZA, Decimal('34.99'), False),
        (Country.GB, Decimal('5.00'), True),
        (Country.GB, Decimal('4.99'), False),
    ],
)
@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch('webapps.booking.models.Business.booksy_pay_available', MagicMock(return_value=True))
@pytest.mark.django_db
def test_is_booksy_pay_available_min_amount(
    country: Country,
    total_value: Decimal,
    expected: bool,
):
    with override_settings(API_COUNTRY=country):
        service_variant = baker.make(
            ServiceVariant,
            type=PriceType.FIXED,
            payment=baker.make(ServiceVariantPayment),
            price=20,
        )
        subbooking_params = dict(
            service_variant=service_variant,
        )
        appointment = create_appointment(
            [subbooking_params],
            status=Appointment.STATUS.ACCEPTED,
            total_type=PriceType.FIXED,
            total_value=total_value,
        )
        assert appointment.is_booksy_pay_available is expected


@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@pytest.mark.parametrize(
    'receipt_status_code, expected',
    [
        (receipt_status.PAYMENT_SUCCESS, False),
        (receipt_status.CHARGEBACK, False),
        (receipt_status.BOOKSY_PAY_FAILED, True),
    ],
)
@pytest.mark.django_db
def test_is_booksy_pay_available__appointment_with_payment(receipt_status_code, expected):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
    )
    transaction = transaction_recipe.make(
        appointment=appointment,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )
    receipt = receipt_recipe.make(
        transaction=transaction,
        status_code=receipt_status_code,
    )
    transaction.latest_receipt = receipt
    transaction.save()
    assert appointment.is_booksy_pay_available is expected


@pytest.mark.parametrize(
    'appointment_status, is_trusted_client, expected',
    [
        (Appointment.STATUS.ACCEPTED, False, False),
        (Appointment.STATUS.FINISHED, False, False),
        (Appointment.STATUS.ACCEPTED, True, True),
        (Appointment.STATUS.FINISHED, True, True),
        (Appointment.STATUS.CANCELED, False, False),
    ],
)
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@patch('webapps.booksy_pay.utils.is_no_show_protection_active', return_value=True)
@pytest.mark.django_db
def test_is_booksy_pay_available__trusted_clients(
    is_no_show_protection_active_mock,
    appointment_status,
    is_trusted_client,
    expected,
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=appointment_status,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
        booked_for__trusted=is_trusted_client,
    )

    assert appointment.is_booksy_pay_available is expected
    if (
        appointment_status in (Appointment.STATUS.ACCEPTED, Appointment.STATUS.FINISHED)
        and is_trusted_client is False
    ):
        is_no_show_protection_active_mock.assert_called_once()
    else:
        is_no_show_protection_active_mock.assert_not_called()


@pytest.mark.parametrize(
    'appointment_status, is_parent_trusted_client, is_member_trusted_client, expected',
    [
        (Appointment.STATUS.ACCEPTED, True, False, False),
        (Appointment.STATUS.ACCEPTED, True, True, True),
        (Appointment.STATUS.ACCEPTED, False, False, False),
        (Appointment.STATUS.ACCEPTED, False, True, False),
        (Appointment.STATUS.FINISHED, True, True, True),
        (Appointment.STATUS.CANCELED, True, True, False),
    ],
)
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@patch('webapps.booksy_pay.utils.is_no_show_protection_active', return_value=True)
@pytest.mark.django_db
def test_is_booksy_pay_available__trusted_clients_family_and_friends(
    is_no_show_protection_active_mock,
    appointment_status,
    is_parent_trusted_client,
    is_member_trusted_client,
    expected,
):
    parent_bci, member_bci = TestFamilyAndFriendsMixin.create_active_members_bcis()
    parent_bci.trusted = is_parent_trusted_client
    member_bci.trusted = is_member_trusted_client
    parent_bci.save()
    member_bci.save()

    appointment = create_appointment_for_member(
        appointment_status=appointment_status,
        parent_bci=parent_bci,
        member_bci=member_bci,
    )
    appointment.total = ServicePrice(value=Decimal('123.00'))
    appointment.save()

    assert appointment.is_booksy_pay_available is expected
    if appointment_status in (Appointment.STATUS.ACCEPTED, Appointment.STATUS.FINISHED) and (
        is_parent_trusted_client is False or is_member_trusted_client is False
    ):
        is_no_show_protection_active_mock.assert_called_once()
    else:
        is_no_show_protection_active_mock.assert_not_called()


@pytest.mark.parametrize(
    'ff, expected',
    [
        (True, True),
        (False, True),
    ],
)
@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@patch(
    'webapps.booking.models.Business.booksy_pay_eligible',
    MagicMock(return_value=True),
)
@patch(
    'webapps.booking.models.Business.booksy_pay_available',
    MagicMock(return_value=True),
)
@pytest.mark.django_db
def test_is_booksy_pay_available__during_booking(ff: bool, expected: bool) -> None:
    """
    Note - there's no appointment commited at this point yet.
    """
    price_value = Decimal('123.00')
    price_type = PriceType.FIXED
    service_variant = service_variant_recipe.make(
        price=price_value,
        type=price_type,
    )
    appointment = create_appointment(
        [{'service_variant': service_variant}],
        status=Appointment.STATUS.ACCEPTED,
        total_value=price_value,
        total_type=price_type,
    )
    # pylint: disable=pointless-statement
    appointment.subbookings  # Prefetch subbookings & cache them
    appointment.id = None if ff is True else appointment.id  # Simulate an uncommited appointment

    with override_eppo_feature_flag({BooksyPayAvailabilityOnBookingFlag.flag_name: ff}):
        assert appointment.is_booksy_pay_available is expected


@patch(
    'webapps.business.models.Business.pos',
    MagicMock(
        stripe_kyc_completed=True,
    ),
)
@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@patch(
    'webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings',
    return_value=MagicMock(enabled=True),
)
@pytest.mark.parametrize(
    'pos__booksy_pay, expected',
    (
        (True, True),
        (False, False),
    ),
)
@pytest.mark.django_db
def test_is_booksy_pay_available__disabled(
    get_settings_mock,
    pos__booksy_pay,
    expected,
):
    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
    )
    appointment.business.pos_enabled = True
    appointment.business.save()

    with override_settings(POS__BOOKSY_PAY=pos__booksy_pay):
        assert appointment.is_booksy_pay_available is expected

    assert get_settings_mock.assert_called_once


@pytest.mark.parametrize(
    'payment_type, receipt_status_code, expected',
    [
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS, True),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.PAYMENT_SUCCESS, True),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.PREPAYMENT_SUCCESS, False),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.CALL_FOR_BOOKSY_PAY_3DS, False),
        (PaymentTypeEnum.PREPAYMENT, receipt_status.PREPAYMENT_SUCCESS, False),
        (PaymentTypeEnum.PREPAYMENT, receipt_status.PAYMENT_SUCCESS, False),
    ],
)
@pytest.mark.django_db
def test_is_paid_by_booksy_pay(
    payment_type: PaymentTypeEnum, receipt_status_code: str, expected: bool
) -> None:
    appointment = create_appointment()
    transaction = transaction_recipe.make(
        appointment=appointment,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )
    pos = pos_recipe.make(business=appointment.business)
    payment_type = payment_type_recipe.make(pos=pos, code=payment_type)
    receipt = receipt_recipe.make(
        transaction=transaction,
        status_code=receipt_status_code,
        payment_type=payment_type,
    )
    transaction.latest_receipt = receipt
    transaction.save()

    assert appointment.is_paid_by_booksy_pay is expected


@pytest.mark.parametrize(
    'payment_type, receipt_status_code, expected',
    [
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS, False),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.PAYMENT_SUCCESS, False),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.PREPAYMENT_SUCCESS, False),
        (PaymentTypeEnum.BOOKSY_PAY, receipt_status.CALL_FOR_BOOKSY_PAY_3DS, True),
        (PaymentTypeEnum.PREPAYMENT, receipt_status.PREPAYMENT_SUCCESS, False),
        (PaymentTypeEnum.PREPAYMENT, receipt_status.PAYMENT_SUCCESS, False),
    ],
)
@pytest.mark.django_db
def test_is_payment_in_progress(
    payment_type: PaymentTypeEnum, receipt_status_code: str, expected: bool
) -> None:
    appointment = create_appointment()
    transaction = transaction_recipe.make(
        appointment=appointment,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )
    pos = pos_recipe.make(business=appointment.business)
    payment_type = payment_type_recipe.make(pos=pos, code=payment_type)
    receipt = receipt_recipe.make(
        transaction=transaction,
        status_code=receipt_status_code,
        payment_type=payment_type,
    )
    transaction.latest_receipt = receipt
    transaction.save()

    assert is_call_for_status(appointment.id) is expected


@patch(
    'webapps.business.models.Business.pos',
    MagicMock(
        stripe_kyc_completed=True,
    ),
)
@patch('webapps.pos.utils.is_no_show_protection_active', MagicMock(return_value=False))
@patch(
    'webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings',
    return_value=MagicMock(enabled=True),
)
@pytest.mark.parametrize(
    'is_booksy_gift_card_appointment_value, expected',
    (
        (True, False),
        (False, True),
    ),
)
@pytest.mark.django_db
@override_eppo_feature_flag({BooksyPaySeparateSettingsFlag.flag_name: True})
def test_is_booksy_gift_card(get_settings_mock, is_booksy_gift_card_appointment_value, expected):

    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    subbooking_params = dict(
        service_variant=service_variant,
    )
    appointment = create_appointment(
        [subbooking_params],
        status=Appointment.STATUS.ACCEPTED,
        total_type=PriceType.FIXED,
        total_value=Decimal('123.00'),
        is_booksy_gift_card_appointment=is_booksy_gift_card_appointment_value,
    )
    appointment.business.pos_enabled = True
    appointment.business.save()

    with override_settings(POS__BOOKSY_PAY=True):
        assert appointment.is_booksy_pay_available is expected

    get_settings_mock.assert_called_once()
