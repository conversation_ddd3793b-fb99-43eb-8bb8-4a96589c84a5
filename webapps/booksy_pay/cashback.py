from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from decimal import Decimal

from django.conf import settings
from django.utils.translation import gettext as _
from django.utils.translation import override

from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import AppDomains, ExperimentVariants
from lib.feature_flag.experiment.booksy_pay import BooksyPayCashbackExperiment
from lib.feature_flag.feature.booksy_pay import BooksyPayCashbackPromoFlag
from webapps.booking.models import Appointment
from webapps.booksy_pay.consts import (
    FIRST_CASHBACK_PROMO_END_DATE,
    SECOND_CASHBACK_PROMO_START_DATE,
)
from webapps.booksy_pay.enums import BooksyPayCashbackAmountType, BooksyPayCashbackType
from webapps.booksy_pay.utils import (
    get_booksy_pay_cashback_recipient,
    has_finished_booksy_pay_transaction,
)


class CashbackReceivedHelper:
    """
    A helper class that is capable of checking if a Cx received any cashback already.
    It relies and shall rely on the BooksyPayCashbackVariant classes logic.

    Note: at this point, only the first promo is expired, hence there's a single method implemented.
    """

    @classmethod
    def has_received_first_cashback_promo(cls, appointment: Appointment) -> bool:
        """
        First cashback promo is the so-called default/initial cashback version
        launched before the cashback experiment.

        Note - if a default cashback is not configured, the default cashback could
        never have been granted - hence `False` returned by default.
        """
        default_cashback = get_booksy_pay_cashback_variant_by_type(BooksyPayCashbackType.DEFAULT)
        return (
            default_cashback.has_received_cashback_already(appointment, False)
            if default_cashback
            else False
        )

    # pylint: disable=unused-argument
    @classmethod
    def has_received_second_cashback_promo(cls, appointment: Appointment) -> bool:
        """
        The logic for the cashback experiment to be implemented after the promo expires.

        RnD required - the so-far identified possible implementations:

        #1 Eppo-based
        It could be rather a short-term solution, until the cashback data is available in the db.
        The idea is to continue querying Eppo after the experiment is officially off to be able
        to determine the cashback option assigned to a Cx. Based on that, the correct conditions
        could be found to run the check against the transactions table (similar to the initial
        promo).

        #2 Eppo variants persisted in the db
        Similar to #1, but with Eppo variants cached in the db. Also a temporary solution. The only
        benefit here would be to stop relying directly on calls to Eppo.

        #3 A periodic task.
        Create a dedicated table to store Booksy Pay cashback data per user. Seed the table with
        the historical data (based on DWH). For new occurrences, periodically (a Celery task)
        call DWH (if feasible) to update the records (e.g. once per hour).

        #4 Utilize Stripe webhooks
        Create a dedicated table to store Booksy Pay cashback data per user. Seed the table with
        the historical data (based on DWH). For new occurrences, utilize the partial-refund Stripe
        webhook events. If one reaches Core, trigger a job that would check if this is related to
        a Booksy Pay transaction and create Booksy Pay Cashback record if needed.
        """


@dataclass(frozen=True)
class BooksyPayCashbackVariant(ABC):
    """
    Abstract class that represents Booksy Pay cashback variant.
    """

    amount_type: BooksyPayCashbackAmountType
    amount: Decimal
    currency: str
    min_booking_amount: Decimal | None
    max_cashback_amount: Decimal | None = field(default=None)
    required_num_of_txns: int | None = field(default=1)
    cashback_type: BooksyPayCashbackType = field(init=False, default=None)
    policy_url: str | None = field(default=None)

    @abstractmethod
    def is_eligible(self, appointment: Appointment, ignore_appointment_txn: bool = False) -> bool:
        """
        Checks if an appointment qualifies for the cashback variant.

        Args:
        appointment: the appointment object.
        ignore_appointment_txn: allows to ignore a transaction related to a given appointment. It's
                                useful while determining the screen that shall be shown for the
                                after payment step (in this scenario, it's needed to know
                                the screen shown before payment).
        """

    @abstractmethod
    def customer_notification_content(self, language) -> str:
        """
        Provides the text for a notification about the cashback variant.
        Concrete classes may have different templates for notification content.
        """

    @abstractmethod
    def has_received_cashback_already(
        self, appointment: Appointment, ignore_appointment_txn: bool = False
    ) -> bool:
        """
        Checks if a Cx was cashback eligible already.

        Note:
            Core does not store info on paid Booksy Pay cashback yet, therefore, this method
            implements a heuristic approach based on the Transaction model data.

        Args:
        appointment: the appointment object.
        ignore_appointment_txn: allows to ignore a transaction related to a given appointment. It's
                                useful while determining the screen that shall be shown for the
                                after payment step (in this scenario, it's needed to know
                                the screen shown before payment).
        """

    def get_policy_url_text(self, language) -> str:
        with override(language):
            return _('Cashback Policy')


@dataclass(frozen=True)
class BooksyPayCashbackDefault(BooksyPayCashbackVariant):
    """
    NOTE:
        THIS CASHBACK PROMO HAS EXPIRED ALREADY (END DATE: 2024-11-18).
        DEFINE NEW DATACLASSES FOR FUTURE PROMOS (EVEN IF SIMILAR CONDITIONS APPLY).
    """

    cashback_type: BooksyPayCashbackType = field(init=False, default=BooksyPayCashbackType.DEFAULT)

    def is_eligible(self, appointment: Appointment, ignore_appointment_txn: bool = False) -> bool:
        return (
            appointment.total_value is not None
            and appointment.total_value >= self.min_booking_amount
            and self.has_received_cashback_already(appointment, ignore_appointment_txn) is False
        )

    def customer_notification_content(self, language) -> str:
        with override(language):
            return _(
                'Pay via card with Booksy Pay to get {amount} {cashback_currency} cashback!'
                ' (Minimum booking {min_booking_amount} {cashback_currency}).'
            ).format(
                amount=round(self.amount),
                cashback_currency=self.currency,
                min_booking_amount=round(self.min_booking_amount),
            )

    def has_received_cashback_already(
        self, appointment: Appointment, ignore_appointment_txn: bool = False
    ) -> bool:
        """
        For the initial cashback version, Cx must have received cashback already,
        if paid with Booksy Pay for an appointment of a total value of 50 PLN.
        Also, the appointment needs to have a finished status.

        Note - it's important to check ONLY transactions before the second cashback
        promo started (i.e. cashback experiment).
        """
        return has_finished_booksy_pay_transaction(
            appointment=appointment,
            txn_min_total_value=self.min_booking_amount,
            num_of_txns=self.required_num_of_txns,
            ignore_appointment_txn=ignore_appointment_txn,
            created_lte=FIRST_CASHBACK_PROMO_END_DATE,
        )


@dataclass(frozen=True)
class BooksyPayCashbackVariantA(BooksyPayCashbackVariant):
    cashback_type: BooksyPayCashbackType = field(
        init=False, default=BooksyPayCashbackType.VARIANT_A
    )

    def is_eligible(self, appointment: Appointment, ignore_appointment_txn: bool = False) -> bool:
        return (
            appointment.total_value is not None
            and appointment.total_value >= self.min_booking_amount
            and self.has_received_cashback_already(appointment, ignore_appointment_txn) is False
        )

    def customer_notification_content(self, language) -> str:
        with override(language):
            return _(
                'Pay via card with Booksy Pay to get {amount} {cashback_currency} cashback!'
                ' (Minimum booking {min_booking_amount} {cashback_currency}).'
            ).format(
                amount=round(self.amount),
                cashback_currency=self.currency,
                min_booking_amount=round(self.min_booking_amount),
            )

    def has_received_cashback_already(
        self, appointment: Appointment, ignore_appointment_txn: bool = False
    ) -> bool:
        """
        For a variant A, Cx must have received cashback already,
        if paid with Booksy Pay for an appointment of a total value of 50 PLN.
        Also, the appointment needs to have a finished status.

        Important:
        If a Cx received the initial/default cashback already (the first cashback promo),
        (s)he SHALL NOT be cashback-eligible again - hence the extra check.
        """
        has_received_first_cashback_promo = (
            CashbackReceivedHelper.has_received_first_cashback_promo(appointment)
        )

        return has_received_first_cashback_promo or has_finished_booksy_pay_transaction(
            appointment=appointment,
            txn_min_total_value=self.min_booking_amount,
            num_of_txns=self.required_num_of_txns,
            ignore_appointment_txn=ignore_appointment_txn,
            created_gte=SECOND_CASHBACK_PROMO_START_DATE,
        )


@dataclass(frozen=True)
class BooksyPayCashbackVariantB(BooksyPayCashbackVariant):
    cashback_type: BooksyPayCashbackType = field(
        init=False, default=BooksyPayCashbackType.VARIANT_B
    )

    def is_eligible(self, appointment: Appointment, ignore_appointment_txn: bool = False) -> bool:
        return self.has_received_cashback_already(appointment, ignore_appointment_txn) is False

    def customer_notification_content(self, language) -> str:
        with override(language):
            return _(
                'Pay via card with Booksy Pay to get {amount} {cashback_currency} cashback!'
            ).format(
                amount=round(self.amount),
                # TODO: just a temporary fix - to be changed in a separate ticket
                # cashback_currency=self.currency,
                cashback_currency='%',
            )

    def has_received_cashback_already(
        self, appointment: Appointment, ignore_appointment_txn: bool = False
    ) -> bool:
        """
        For a variant B, Cx must have received cashback already,
        if paid with Booksy Pay for an appointment of any total value.
        Also, the appointment needs to have a finished status.

        Important:
        If a Cx received the initial/default cashback already (the first cashback promo),
        (s)he SHALL NOT be cashback-eligible again - hence the extra check.
        """
        has_received_first_cashback_promo = (
            CashbackReceivedHelper.has_received_first_cashback_promo(appointment)
        )

        return has_received_first_cashback_promo or has_finished_booksy_pay_transaction(
            appointment=appointment,
            txn_min_total_value=None,
            num_of_txns=self.required_num_of_txns,
            ignore_appointment_txn=ignore_appointment_txn,
            created_gte=SECOND_CASHBACK_PROMO_START_DATE,
        )


@dataclass(frozen=True)
class BooksyPayCashbackVariantC(BooksyPayCashbackVariant):
    cashback_type: BooksyPayCashbackType = field(
        init=False, default=BooksyPayCashbackType.VARIANT_C
    )

    def is_eligible(self, appointment: Appointment, ignore_appointment_txn: bool = False) -> bool:
        return (
            appointment.total_value is not None
            and appointment.total_value >= self.min_booking_amount
            and self.has_received_cashback_already(appointment, ignore_appointment_txn) is False
        )

    def customer_notification_content(self, language) -> str:
        with override(language):
            return _(
                'Pay via card with Booksy Pay to get {amount} {cashback_currency} cashback!'
                ' (Minimum booking {min_booking_amount} {cashback_currency}).'
            ).format(
                amount=round(self.amount),
                cashback_currency=self.currency,
                min_booking_amount=round(self.min_booking_amount),
            )

    def has_received_cashback_already(
        self, appointment: Appointment, ignore_appointment_txn: bool = False
    ) -> bool:
        """
        For a variant C, Cx must have received cashback already,
        if paid with Booksy Pay for TWO appointments of a total value of 50 PLN each.
        Also, the appointment needs to have a finished status.

        Important:
        If a Cx received the initial/default cashback already (the first cashback promo),
        (s)he SHALL NOT be cashback-eligible again - hence the extra check.
        """
        has_received_first_cashback_promo = (
            CashbackReceivedHelper.has_received_first_cashback_promo(appointment)
        )

        return has_received_first_cashback_promo or has_finished_booksy_pay_transaction(
            appointment=appointment,
            txn_min_total_value=self.min_booking_amount,
            num_of_txns=self.required_num_of_txns,
            ignore_appointment_txn=ignore_appointment_txn,
            created_gte=SECOND_CASHBACK_PROMO_START_DATE,
        )


def get_booksy_pay_cashback_variant_by_type(
    booksy_pay_cashback_type: BooksyPayCashbackType | None = None,
) -> BooksyPayCashbackVariant | None:
    """
    It does not perform any extra checks - just gets a variant by the explicitly provided type.
    """
    return CASHBACK_VARIANTS.get(booksy_pay_cashback_type)


def get_booksy_pay_cashback_variant_by_appt(
    appointment: Appointment,
) -> BooksyPayCashbackVariant | None:
    """
    Get Booksy Pay cashback variant for particular appointment according to cashback type.

    Return None when cashback settings is missing or cashback is not eligible for appointment.
    """
    if not (user := get_booksy_pay_cashback_recipient(appointment)):
        return

    # Here, a cashback type gets assigned by Eppo
    booksy_pay_cashback_type = get_booksy_pay_cashback_type(user_id=int(user.id))
    cashback_variant = get_booksy_pay_cashback_variant_by_type(booksy_pay_cashback_type)

    if isinstance(cashback_variant, BooksyPayCashbackDefault) and not BooksyPayCashbackPromoFlag():
        # Note - `BooksyPayCashbackPromoFlag` is the flag added for the first cashback promo, hence
        # it needs to be checked here to avoid returning the default cashback after it's expired.
        return

    return cashback_variant


def create_cashback_variants_from_settings():
    """
    Creates a dictionary with cashback variants from BOOKSY_PAY_CASHBACK settings.
    Must be run during the first module import.
    """
    return {
        BooksyPayCashbackType(type_name): CASHBACK_TYPE_VARIANT_MAPPING[type_name](**data)
        for type_name, data in settings.BOOKSY_PAY_CASHBACK.items()
    }


def get_booksy_pay_cashback_type(user_id: int) -> BooksyPayCashbackType | None:
    experiment_variant_eppo = BooksyPayCashbackExperiment(
        UserData(
            subject_key=user_id,
            subject_type='user_id',
            is_experiment=True,
            app_domain=AppDomains.CUSTOMER.value,
        )
    )

    try:
        experiment_variant = ExperimentVariants(experiment_variant_eppo)
    except ValueError:
        experiment_variant = None

    try:
        return EPPO_TO_BOOKSY_PAY_CASHBACK_TYPE[experiment_variant]
    except KeyError:
        return None


CASHBACK_TYPE_VARIANT_MAPPING = {
    BooksyPayCashbackType.DEFAULT: BooksyPayCashbackDefault,
    BooksyPayCashbackType.VARIANT_A: BooksyPayCashbackVariantA,
    BooksyPayCashbackType.VARIANT_B: BooksyPayCashbackVariantB,
    BooksyPayCashbackType.VARIANT_C: BooksyPayCashbackVariantC,
}
EPPO_TO_BOOKSY_PAY_CASHBACK_TYPE = {
    # Initial cashback promo logic - left for backward-compatibility
    None: BooksyPayCashbackType.DEFAULT,  # Experiment is off, serve previous logic
    # New cashback promo logic (Eppo-based)
    ExperimentVariants.CONTROL: None,  # Control Group - no cashback promo
    ExperimentVariants.VARIANT_A: BooksyPayCashbackType.VARIANT_A,
    ExperimentVariants.VARIANT_B: BooksyPayCashbackType.VARIANT_B,
    ExperimentVariants.VARIANT_C: BooksyPayCashbackType.VARIANT_C,
}
BOOKSY_PAY_CASHBACK_TYPE_TO_EPPO = {v: k for k, v in EPPO_TO_BOOKSY_PAY_CASHBACK_TYPE.items()}
CASHBACK_VARIANTS = create_cashback_variants_from_settings()
