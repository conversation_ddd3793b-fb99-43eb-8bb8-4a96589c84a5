from lib.enums import StrEnum


class BooksyPayCashbackType(StrEnum):
    """
    Note - the control group won't be shown any cashback info.
    """

    DEFAULT = 'default'
    VARIANT_A = 'variant_a'
    VARIANT_B = 'variant_b'
    VARIANT_C = 'variant_c'


class BooksyPayCashbackAmountType(StrEnum):
    FIXED = 'fixed'
    PERCENTAGE = 'percentage'


class BooksyPayGroupedStatus(StrEnum):
    ACTIVE = 'active'
    NEW = 'new'
    VERIFICATION_PENDING = 'verification_pending'
