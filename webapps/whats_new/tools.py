import typing as t
from datetime import timedelta

from django.conf import settings
from django.db.models import Q

from lib.tools import tznow
from webapps.booking.models import BookingSources
from webapps.business.models import Business, QuerySet
from webapps.user.models import User
from webapps.whats_new.consts import WHATS_NEW_LANGUAGES, AppType
from webapps.whats_new.models import WhatsNew, UserSeenWhatsNew, WhatsNewContent


def new_business(business: Business) -> bool:
    return business.created + timedelta(days=1) >= tznow()


# pylint: disable=too-many-arguments
def get_whats_new_contents(
    language: str,
    limit: int,
    feature_keys: t.List[str],
    booking_source: BookingSources,
    user: User,
    business: t.Optional[Business] = None,
) -> QuerySet:
    if business and new_business(business):
        return WhatsNew.objects.none()

    whats_new_contents = available_whats_new_contents(booking_source, language)
    whats_new_contents = whats_new_contents.filter(
        whats_new__feature_key__name__in=feature_keys,
    )

    seen_whats_new_ids = (
        UserSeenWhatsNew.objects.filter(user=user).values_list('whats_new_ids', flat=True).first()
        or []
    )

    whats_new_contents = whats_new_contents.exclude(whats_new_id__in=seen_whats_new_ids)

    return whats_new_contents[:limit]


def save_user_seen_whats_news(user: User, booking_source: BookingSources, language: str):
    user_seen_whats_new, _ = UserSeenWhatsNew.objects.get_or_create(user=user)

    whats_new_contents = available_whats_new_contents(booking_source, language)
    visible_whats_new_feature_keys = whats_new_contents.exclude(
        whats_new_id__in=user_seen_whats_new.whats_new_ids
    ).values_list('whats_new__feature_key__name', flat=True)

    marked_seen_whats_new_ids = list(
        WhatsNew.objects.filter(feature_key__name__in=visible_whats_new_feature_keys).values_list(
            'id', flat=True
        )
    )

    user_seen_whats_new.whats_new_ids.extend(marked_seen_whats_new_ids)
    user_seen_whats_new.save()


def available_whats_new_contents(booking_source, language) -> QuerySet:
    return WhatsNewContent.objects.filter(
        Q(whats_new__visible_till__isnull=True) | Q(whats_new__visible_till__gte=tznow()),
        whats_new__app_type=AppType.from_source(booking_source),
        whats_new__visible_from__lte=tznow(),
        language=language,
    ).order_by('-whats_new__visible_from')


def determine_content_language(  # pylint: disable=too-many-return-statements
    language: str,
) -> t.Optional[str]:
    available_languages = [lang[0] for lang in WHATS_NEW_LANGUAGES if lang[0].startswith(language)]

    if not available_languages:
        return None

    language_with_country_code = f'{language}-{settings.COUNTRY_CONFIG.country_code}'
    if language_with_country_code in available_languages:
        return language_with_country_code

    if language in available_languages:
        return language

    if language == 'en':
        return 'en-us'

    return None
