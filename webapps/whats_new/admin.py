from collections import Counter

from django import forms
from django.contrib import admin
from django.core.exceptions import ValidationError

from lib.admin_helpers import BaseModelAdmin
from lib.tools import tznow
from webapps.whats_new.consts import WHATS_NEW_APP_TYPE_CHOICES, WHATS_NEW_LANGUAGES
from webapps.whats_new.models import UserSeenWhatsNew, WhatsNew, WhatsNewContent, WhatsNewFeatureKey


class WhatsNewContentFormset(forms.BaseInlineFormSet):
    def add_fields(self, form, index):
        super().add_fields(form, index)
        form.fields["header"] = forms.CharField(
            required=True, widget=forms.Textarea(attrs=dict(rows=4))
        )
        form.fields["body"] = forms.CharField(required=True, widget=forms.Textarea)

    def clean(self):
        if hasattr(self, 'cleaned_data'):
            contents = self.cleaned_data
            if len(contents) == 1 and contents[0] == {}:
                raise forms.ValidationError('At least one content is required')

            counter = Counter(content.get('language') for content in contents)
            for key in counter:
                if counter[key] > 1:
                    invalid_language = dict(WHATS_NEW_LANGUAGES)[key]
                    raise forms.ValidationError(
                        f'Language {invalid_language} must be specified only one time'
                    )

        return super().clean()


class WhatsNewContentInline(admin.StackedInline):
    model = WhatsNewContent
    verbose_name = 'Whats new content for language'
    readonly_fields = ('created', 'updated', 'deleted')
    classes = ('collapses',)
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('language',),
                    ('header',),
                    ('body',),
                    ('image',),
                ),
            },
        ),
        (
            'Info',
            {
                'classes': ('collapse',),
                'fields': ('created', 'updated', 'deleted'),
            },
        ),
    )
    formset = WhatsNewContentFormset
    extra = 1


class WhatsNewForm(forms.ModelForm):
    class Meta:
        model = WhatsNew
        fields = ('app_type', 'feature_key', 'visible_from', 'visible_till')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['feature_key'].queryset = WhatsNewFeatureKey.objects.all()

    def clean(self):
        cleaned_data = super().clean()
        visible_from = cleaned_data.get('visible_from')
        if visible_from:
            if visible_from < tznow():
                raise ValidationError("Visible from must be in future")

            visible_till = cleaned_data.get('visible_till')
            if visible_till and visible_till < visible_from:
                raise ValidationError("Visible till must be gt visible from")

        return cleaned_data


class WhatsNewApplicationTypeFilter(admin.SimpleListFilter):
    title = 'Application type'
    parameter_name = 'app_type'

    def lookups(self, request, model_admin):
        return WHATS_NEW_APP_TYPE_CHOICES

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        return queryset.filter(app_type=self.value())


class WhatsNewAdmin(BaseModelAdmin):
    list_display = (
        'id',
        'feature_key',
        'app_type',
        'header',
        'visible_from',
        'visible_till',
    )
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'app_type',
                    'feature_key',
                    'visible_from',
                    'visible_till',
                ),
            },
        ),
        (
            'Info',
            {
                'classes': ('collapse',),
                'fields': ('created', 'updated', 'deleted'),
            },
        ),
    )
    readonly_fields = [
        'id',
        'created',
        'updated',
        'deleted',
    ]

    list_filter = (
        'feature_key',
        WhatsNewApplicationTypeFilter,
        'visible_from',
        'visible_till',
    )

    inlines = [
        WhatsNewContentInline,
    ]
    form = WhatsNewForm

    @staticmethod
    def header(obj):
        if obj.contents:
            content = obj.contents.first()
            return content.header


class UserSeenWhatsNewAdmin(BaseModelAdmin):
    search_fields = ('user__id',)
    list_display = ('id', 'user')
    fields = ('user', 'whats_new_ids')
    raw_id_fields = ('user',)


class WhatsNewFeatureKeyAdmin(BaseModelAdmin):
    fields = ('name',)
    list_display = ('name',)


admin.site.register(WhatsNew, WhatsNewAdmin)
admin.site.register(UserSeenWhatsNew, UserSeenWhatsNewAdmin)
admin.site.register(WhatsNewFeatureKey, WhatsNewFeatureKeyAdmin)
