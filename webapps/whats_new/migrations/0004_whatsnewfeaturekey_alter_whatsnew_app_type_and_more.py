# Generated by Django 4.0.2 on 2022-04-26 07:42

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('whats_new', '0003_alter_whatsnewcontent_language'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsNewFeatureKey',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=48, unique=True)),
            ],
            options={
                'verbose_name_plural': '3. Feature keys',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AlterField(
            model_name='whatsnew',
            name='app_type',
            field=models.CharField(
                choices=[
                    ('BUSINESS_Frontdesk', 'BUSINESS_FRONTDESK'),
                    ('BUSINESS_Mobile', 'BUSINESS_MOBILE'),
                    ('CUSTOMER_WEB', 'CUSTOMER_WEB'),
                    ('CUSTOMER_Android', 'CUSTOMER_ANDROID'),
                    ('CUSTOMER_IOS', 'CUSTOMER_IOS'),
                ],
                db_index=True,
                help_text='Where Mobile = Android and iOS Biz Mobile Apps',
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name='whatsnew',
            name='feature_key_fk',
            field=models.ForeignKey(
                blank=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to='whats_new.whatsnewfeaturekey',
            ),
        ),
    ]
