# Generated by Django 3.2.7 on 2021-10-28 13:32

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import lib.models
import settings.storage
import webapps.images.tools


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user', '0052_apple_transfer_helper_fixes'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSeenWhatsNew',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'whats_new_ids',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(),
                        default=list,
                        help_text='IDs, separated by commas without spaces (1,2,3)',
                        size=None,
                    ),
                ),
            ],
            options={
                'verbose_name_plural': "2. Users who saw What's new",
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='WhatsNew',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('feature_key', models.CharField(db_index=True, max_length=48)),
                (
                    'app_type',
                    models.CharField(
                        choices=[('Frontdesk', 'FRONTDESK'), ('Mobile', 'MOBILE')],
                        db_index=True,
                        help_text='Where Mobile = Android and iOS Biz Mobile Apps',
                        max_length=10,
                    ),
                ),
                (
                    'visible_from',
                    models.DateTimeField(
                        blank=True,
                        help_text="Starting this date, what's new content is visible and active. WARNING! The content  isn't live if the field is empty.",
                        null=True,
                        verbose_name='Visible from (UTC)',
                    ),
                ),
                (
                    'visible_till',
                    models.DateTimeField(blank=True, null=True, verbose_name='Visible till (UTC)'),
                ),
            ],
            options={
                'verbose_name_plural': "1. What's new",
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='WhatsNewContent',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'language',
                    models.CharField(
                        choices=[
                            ('en-gb', 'English (Great Britain)'),
                            ('en-us', 'English (USA)'),
                            ('es', 'Spanish'),
                            ('es-mx', 'Spanish (Mexico)'),
                            ('fr', 'French'),
                            ('pl', 'Polish'),
                            ('pt', 'Portuguese'),
                            ('pt-br', 'Portuguese (Brazil)'),
                            ('vi', 'Vietnamese'),
                        ],
                        max_length=5,
                    ),
                ),
                (
                    'image',
                    models.ImageField(
                        storage=settings.storage.WhatsNewImageStorage(),
                        upload_to=webapps.images.tools.get_whats_new_image_path,
                    ),
                ),
                ('header', models.CharField(max_length=105)),
                ('body', models.CharField(max_length=500)),
                (
                    'whats_new',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='contents',
                        to='whats_new.whatsnew',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddConstraint(
            model_name='whatsnew',
            constraint=models.UniqueConstraint(
                condition=models.Q(('deleted__isnull', True)),
                fields=('feature_key', 'app_type'),
                name='feature_key_and_app_type_unique',
            ),
        ),
        migrations.AddField(
            model_name='userseenwhatsnew',
            name='user',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='seen_whats_news',
                to='user.user',
            ),
        ),
        migrations.AddConstraint(
            model_name='whatsnewcontent',
            constraint=models.UniqueConstraint(
                condition=models.Q(('deleted__isnull', True)),
                fields=('whats_new', 'language'),
                name='whats_new_and_language_unique',
            ),
        ),
    ]
