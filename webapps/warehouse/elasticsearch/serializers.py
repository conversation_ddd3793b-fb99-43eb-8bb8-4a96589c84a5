from rest_framework import serializers
from webapps.warehouse.elasticsearch.commodities import (
    CommodityCategoryDocument,
)
from lib.elasticsearch.hit_serializer import HitSerializer


class CommodityCategoryDocumentHitSerializer(HitSerializer):

    class Meta:
        document = CommodityCategoryDocument
        fields = (
            'parent_id',
            'category_name',
            'id',
            'business_id',
            'order',
            'products__count',
            'retail_products',
            'children',
            'all_ancestors',
            'all_descendants',
            'top_level_ancestor',
        )

    id = serializers.IntegerField()
    order = serializers.IntegerField()
    category_name = serializers.CharField()
    parent_id = serializers.IntegerField()
    business_id = serializers.IntegerField()
    products__count = serializers.IntegerField()
    retail_products = serializers.BooleanField()
    all_ancestors = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
    )
    all_descendants = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
    )
    top_level_ancestor = serializers.IntegerField()
