import elasticsearch_dsl as dsl
from django.db.models import Q
from django.db.models.signals import post_save
from rest_framework import serializers

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.document import Document
from lib.elasticsearch.index import Index
from lib.rivers import River, bump_on_signal
from settings import elasticsearch as es_settings
from webapps.warehouse.models import (
    Commodity,
    CommodityCategory,
)


class CommodityCategoryDocumentSerializer(serializers.ModelSerializer):

    class Meta:
        model = CommodityCategory
        fields = (
            'id',
            'all_ancestors',
            'all_descendants',
            'business_id',
            'category_name',
            'order',
            'parent_id',
            'products__count',
            'retail_products',
            'top_level_ancestor',
        )

    id = serializers.IntegerField()
    order = serializers.IntegerField()
    category_name = serializers.CharField(source='name')
    parent_id = serializers.IntegerField()
    business_id = serializers.IntegerField()
    products__count = serializers.SerializerMethodField()
    retail_products = serializers.SerializerMethodField()
    all_ancestors = serializers.SerializerMethodField()
    all_descendants = serializers.SerializerMethodField()
    top_level_ancestor = serializers.SerializerMethodField()

    @staticmethod
    def get_retail_products(instance):
        return Commodity.objects.filter(
            product_type=Commodity.TYPE_RETAIL,
            archived=False,
            deleted__isnull=True,
            category=instance,
        ).exists()

    def get_all_ancestors(self, instance):  # todo optimize??
        parent_id = instance.parent_id
        if not parent_id:
            return []
        return [parent_id, *self.get_all_ancestors(instance.parent)]

    def get_all_descendants(self, instance):  # todo optimize??
        children = instance.children.all().only('id')
        children_ids_list = list(children.values_list('id', flat=True))
        if children_ids_list:
            for child_category in children:
                children_ids_list.extend(self.get_all_descendants(child_category))
        return children_ids_list

    def get_top_level_ancestor(self, instance):  # todo optimize??
        parent_id = instance.parent_id
        if not parent_id:
            # if no parent then top level ancestor is this instance
            return instance.id
        return self.get_top_level_ancestor(instance.parent) or parent_id

    def get_products__count(self, instance):
        descendants = self.get_all_descendants(instance)
        descendants.append(instance.id)
        return Commodity.objects.filter(
            Q(category__in=descendants) | Q(extra_categories__in=descendants),
            archived=False,
            deleted__isnull=True,
        ).count()


class CommodityCategoryDocument(Document):
    class Meta:
        id = 'id'
        routing = 'business_id'
        parent = 'parent_id'
        model = CommodityCategory
        serializer = CommodityCategoryDocumentSerializer
        # TODO optimize queryset with prefetch
        queryset = CommodityCategory.objects.filter(
            deleted__isnull=True,
        )

        es_bulk_size = 100

    id = dsl.Integer(index=True)
    business_id = dsl.Integer(index=True)
    category_name = dsl.Text(index=True)
    order = dsl.Integer(index=True)
    parent_id = dsl.Integer()
    products__count = dsl.Integer(index=True)
    retail_products = dsl.Boolean(index=True)

    all_ancestors = dsl.Integer(index=False, multi=True)
    all_descendants = dsl.Integer(index=False, multi=True)
    top_level_ancestor = dsl.Integer(index=False)


class WarehouseItemIndex(Index):
    no_suffix_name = 'warehouse_item_index'
    name = f'{no_suffix_name}_{es_settings.ES_SUFFIX}'
    index_settings = dict(
        max_ngram_diff=20,
    )
    documents = {
        ESDocType.COMMODITY_CATEGORY: CommodityCategoryDocument,
    }


bump_on_signal(
    River.COMMODITY_CATEGORY,
    post_save,
    'warehouse.CommodityCategory',
    'id',
)
