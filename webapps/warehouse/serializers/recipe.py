from django.db import transaction
from rest_framework import serializers
from webapps.business.models import ServiceVariant
from webapps.warehouse.models import (
    WarehouseFormula,
    WarehouseFormulaRow,
    Commodity,
)
from webapps.warehouse.serializers.common import HistoryChangeSerializerMixin


class WarehouseRecipeValidationMixin:

    def validate_variant_id(self, value):
        if not ServiceVariant.objects.filter(
            id=value,
            service__business_id=self.context['business_id'],
        ).exists():
            raise serializers.ValidationError("Invalid variant_id")
        return value


class WarehouseRecipesDeleteRequestSerializer(
    WarehouseRecipeValidationMixin, serializers.Serializer
):
    variant_id = serializers.IntegerField()
    formula_id = serializers.IntegerField()

    def validate_formula_id(self, value):
        if not WarehouseFormula.objects.filter(
            id=value,
            service_variants__service__business_id=self.context['business_id'],
            service_variants__id=self.initial_data['variant_id'],
        ).exists():
            raise serializers.ValidationError('Invalid formula_id')
        return value


class WarehouseRecipesGetRequestSerializer(WarehouseRecipeValidationMixin, serializers.Serializer):
    variant_id = serializers.IntegerField()


class UsageCommoditySerializer(serializers.ModelSerializer):

    class Meta:
        model = Commodity
        fields = [
            'id',
            'name',
            'volume_unit',
            'total_pack_capacity',
        ]


class BookingCommodityUsageSerializer(serializers.Serializer):

    commodity = UsageCommoditySerializer()
    count = serializers.IntegerField()
    warehouse = serializers.IntegerField()


class FormulaCommoditySerializer(serializers.PrimaryKeyRelatedField):
    """This serializer just picks few fields from standard
    CommodityDetailsSerializer in fact it should just inherit from it and
    specify fields explicitly in __init__(fields=[]).

    However we can't import it here directly at module level due to circular
    imports: warehouse.serializers.other imports from here and that's why
    here we can't import from warehouse.serializers.other, what a mess...
    """

    def get_queryset(self):
        return Commodity.all_objects.all()

    def to_representation(self, value):
        # pylint: disable=cyclic-import
        from webapps.warehouse.serializers.other import (
            CommodityDetailsSerializer,
        )

        if isinstance(value, dict):
            commodity = value
        else:
            commodity = Commodity.all_objects.filter(
                id=value.pk,
            ).first()
        serializer = CommodityDetailsSerializer(
            instance=commodity,
            fields=[
                'id',
                'name',
                'total_pack_capacity',
                'volume_unit',
                'archived',
            ],
        )
        return serializer.data


class WarehouseRecipesRowSerializer(
    HistoryChangeSerializerMixin,
    serializers.ModelSerializer,
):
    id = serializers.IntegerField(required=False)
    commodity = FormulaCommoditySerializer()

    class Meta:
        model = WarehouseFormulaRow
        fields = [
            'id',
            'commodity',
            'count',
            'warehouse',
        ]


class FastWarehouseRecipesRowSerializer(WarehouseRecipesRowSerializer):
    warehouse = serializers.IntegerField()


class RecipesSerializer(
    HistoryChangeSerializerMixin,
    serializers.ModelSerializer,
):
    rows = WarehouseRecipesRowSerializer(many=True)

    class Meta:
        model = WarehouseFormula
        fields = [
            'id',
            'rows',
        ]

    def create(self, validated_data):
        rows = validated_data.pop('rows', [])
        with transaction.atomic():
            recipe_object = super().create(validated_data)
            rows_objects = []
            for row in rows:
                row_serializer = WarehouseRecipesRowSerializer(
                    data={
                        'commodity': row['commodity'].id,
                        'count': row['count'],
                        'warehouse': row['warehouse'].id,
                    },
                    context=self.context,
                )
                if not row_serializer.is_valid():
                    raise serializers.ValidationError("Row error")
                row_object = row_serializer.save()
                rows_objects.append(row_object)
            recipe_object.rows.set(rows_objects)
            recipe_object.save()
        return recipe_object


class ServiceVariantFormulaSerializer(RecipesSerializer):

    class Meta:
        model = WarehouseFormula
        fields = [
            'id',
            'service_variants',
            'rows',
        ]


class ServiceVariantFormulaModifySerializer(ServiceVariantFormulaSerializer):
    id = serializers.IntegerField(required=True)
