# Generated by Django 2.0.13 on 2019-10-17 14:51

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0069_headcase_api_key'),
        ('business', '0240_auto_20191009_1002'),
        # ('pos', '0161_merge_20190801_1336'),
        ('warehouse', '0044_warehouse_recipe'),
    ]

    operations = [
        migrations.CreateModel(
            name='WarehouseFormula',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'history_change',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list, null=True
                    ),
                ),
                (
                    'bookings',
                    models.ManyToManyField(blank=True, db_constraint=False, to='booking.Booking'),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='WarehouseFormulaRow',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'history_change',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list, null=True
                    ),
                ),
                ('count', models.FloatField(default=0)),
                (
                    'commodity',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='warehouse.Commodity'
                    ),
                ),
                (
                    'transactions',
                    models.ManyToManyField(blank=True, db_constraint=False, to='pos.Transaction'),
                ),
                (
                    'warehouse',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='warehouse.Warehouse'
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='warehouserecipe',
            name='bookings',
        ),
        migrations.RemoveField(
            model_name='warehouserecipe',
            name='rows',
        ),
        migrations.RemoveField(
            model_name='warehouserecipe',
            name='service_variants',
        ),
        migrations.RemoveField(
            model_name='warehousereciperow',
            name='commodity',
        ),
        migrations.DeleteModel(
            name='WarehouseRecipe',
        ),
        migrations.DeleteModel(
            name='WarehouseRecipeRow',
        ),
        migrations.AddField(
            model_name='warehouseformula',
            name='rows',
            field=models.ManyToManyField(
                blank=True,
                to='warehouse.WarehouseFormulaRow',
            ),
        ),
        migrations.AddField(
            model_name='warehouseformula',
            name='service_variants',
            field=models.ManyToManyField(
                blank=True,
                db_constraint=False,
                to='business.ServiceVariant',
            ),
        ),
    ]
