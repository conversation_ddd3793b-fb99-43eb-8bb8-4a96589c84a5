# Generated by Django 2.0.13 on 2019-08-30 07:33

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0035_alter_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='StocktakingDocument',
            fields=[
                (
                    'basewarehousedocument_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='warehouse.BaseWarehouseDocument',
                    ),
                ),
                (
                    'history_change',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list, null=True
                    ),
                ),
                (
                    'acceptance_date',
                    models.DateField(blank=True, null=True, verbose_name='Acceptance date'),
                ),
                (
                    'warehouse',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='stocktakings',
                        to='warehouse.Warehouse',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            bases=('warehouse.basewarehousedocument', models.Model),
        ),
        migrations.CreateModel(
            name='StocktakingDocumentRow',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'history_change',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list, null=True
                    ),
                ),
                ('commodity_name', models.CharField(max_length=200)),
                ('initial_volume', models.DecimalField(decimal_places=2, default=1, max_digits=6)),
                (
                    'initial_packages',
                    models.DecimalField(decimal_places=2, default=1, max_digits=6),
                ),
                (
                    'quantity_inventoried',
                    models.DecimalField(decimal_places=2, default=1, max_digits=6),
                ),
                ('is_full_package_inventoried', models.BooleanField()),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                (
                    'commodity',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='warehouse.Commodity'
                    ),
                ),
                (
                    'document',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='rows',
                        to='warehouse.StocktakingDocument',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='basewarehousedocument',
            name='type',
            field=models.CharField(
                choices=[
                    ('WZ', 'Stock issue confirmation'),
                    ('RW', 'Internal expenditure'),
                    ('MM', 'Inter stock transfer'),
                    ('PZ', 'External stock reception'),
                    ('ORD', 'Order'),
                    ('INW', 'Stocktaking'),
                ],
                max_length=3,
            ),
        ),
    ]
