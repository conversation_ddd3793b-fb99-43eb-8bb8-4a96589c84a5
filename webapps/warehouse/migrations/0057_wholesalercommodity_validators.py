# Generated by Django 2.0.13 on 2020-01-08 15:09

from django.db import migrations, models
from django.core.validators import MinValueValidator


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0056_merge_20191218_1044'),
    ]

    operations = [
        migrations.AlterField(
            model_name='wholesalercommodity',
            name='gross_price',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, validators=[MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name='wholesalercommodity',
            name='net_price',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, validators=[MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name='wholesalercommodity',
            name='tax',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, validators=[MinValueValidator(0)]
            ),
        ),
    ]
