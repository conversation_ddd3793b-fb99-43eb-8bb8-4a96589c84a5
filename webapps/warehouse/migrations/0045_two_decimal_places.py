# Generated by Django 2.0.13 on 2019-11-04 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0044_warehousedocument_rw_reason'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='current_net_purchase_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='gross_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='net_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='supplyrow',
            name='gross_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='supplyrow',
            name='net_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='supplyrow',
            name='tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='warehousedocumentrow',
            name='gross_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='warehousedocumentrow',
            name='net_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='warehousedocumentrow',
            name='tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
    ]
