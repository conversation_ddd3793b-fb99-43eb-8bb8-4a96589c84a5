# Generated by Django 2.0.13 on 2020-03-03 09:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0078_make_issuing_staffer_nullable'),
        ('booking', '0084_rename_booking_mulitbooking_and_add_new_columns'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='bookings',
            field=models.ManyToManyField(
                blank=True, related_name='commodities', to='booking.SubBooking'
            ),
        ),
        migrations.AlterField(
            model_name='warehousedocument',
            name='booking',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to='booking.SubBooking',
            ),
        ),
        migrations.AlterField(
            model_name='warehouseformula',
            name='bookings',
            field=models.ManyToManyField(blank=True, db_constraint=False, to='booking.SubBooking'),
        ),
        # manual alter name if it's not
        migrations.RunSQL(
            """
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT column_name FROM information_schema.columns WHERE table_name='warehouse_commodity_bookings' AND column_name='subbooking_id') THEN
                        ALTER TABLE warehouse_commodity_bookings RENAME booking_id to subbooking_id;
                    END IF;
                END $$;
            """,
            "",
        ),
        migrations.RunSQL(
            """
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT column_name FROM information_schema.columns WHERE table_name='warehouse_warehouseformula_bookings' AND column_name='subbooking_id') THEN
                        ALTER TABLE warehouse_warehouseformula_bookings RENAME booking_id to subbooking_id;
                    END IF;
                END $$;
            """,
            "",
        ),
    ]
