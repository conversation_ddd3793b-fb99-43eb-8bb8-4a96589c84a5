# Generated by Django 2.0.13 on 2020-02-24 11:02
import time
import datetime
from django.db import migrations


def migrate_old_receipts_transactions(apps, schema_editor):
    db_name = schema_editor.connection.alias
    Resource = apps.get_model('business', 'Resource')
    Warehouse = apps.get_model('warehouse', 'Warehouse')
    Commodity = apps.get_model('warehouse', 'Commodity')
    Transaction = apps.get_model('pos', 'Transaction')
    TransactionRow = apps.get_model('pos', 'TransactionRow')
    WarehouseDocumentRow = apps.get_model('warehouse', 'WarehouseDocumentRow')
    WarehouseDocument = apps.get_model('warehouse', 'WarehouseDocument')
    BaseWarehouseDocument = apps.get_model('warehouse', 'BaseWarehouseDocument')
    start_time = time.time()

    transaction_rows = (
        TransactionRow.objects.using(db_name)
        .filter(
            product__isnull=False,
        )
        .prefetch_related('product', 'transaction')
    )
    transactions_ids = transaction_rows.values_list('transaction', flat=True).distinct()
    transactions_ids = set(transactions_ids) - set(
        WarehouseDocument.objects.using(db_name).values_list('transaction', flat=True)
    )

    transactions = (
        Transaction.objects.using(db_name)
        .filter(
            id__in=transactions_ids,
            # because of performance during upgrade
            created__gte=datetime.datetime(2019, 11, 1, 0, 0),
        )
        .distinct()
        .prefetch_related('pos__business', 'operator')
    )
    wz_documents_by_transaction_id = {}
    base_documents_by_transaction_id = {}
    print("--- Start migration for Documents assigned to old Recipe ---")
    for transaction in transactions:
        business = transaction.pos.business
        issuing_staffer = (
            Resource.objects.using(db_name)
            .filter(
                staff_user=transaction.operator,
            )
            .first()
            or Resource.objects.using(db_name).filter(staff_user=business.owner).first()
        )
        if not issuing_staffer:
            continue
        base_document = BaseWarehouseDocument(issuing_staffer=issuing_staffer, type='WZ')
        base_documents_by_transaction_id[transaction.id] = base_document

    BaseWarehouseDocument.objects.using(db_name).bulk_create(
        list(base_documents_by_transaction_id.values()),
        1000,
    )
    print("--- BaseWarehouseDocuments created ---")

    for transaction in transactions:
        business = transaction.pos.business
        warehouse = (
            Warehouse.objects.using(db_name)
            .filter(
                business=business,
                is_default=True,
            )
            .first()
        )
        base_document = base_documents_by_transaction_id.get(transaction.id)
        if not base_document:
            continue
        wz_document = WarehouseDocument(
            basewarehousedocument_ptr=base_document,
            transaction=transaction,
            warehouse=warehouse,
            hidden=True,
        )
        wz_documents_by_transaction_id[transaction.id] = wz_document

    WarehouseDocument.objects.using(db_name).bulk_create(
        list(wz_documents_by_transaction_id.values()),
        1000,
    )
    print("--- WarehouseDocuments created ---")

    wz_rows = []

    for transaction_row in transaction_rows:
        try:
            commodity = transaction_row.product
        except Commodity.DoesNotExist:
            continue
        if not commodity:
            continue
        wz_document = wz_documents_by_transaction_id.get(
            transaction_row.transaction.id,
        )
        if not wz_document:
            continue

        wz_row = WarehouseDocumentRow(
            commodity_name=commodity.name,
            commodity=transaction_row.product,
            quantity=transaction_row.quantity or 1,
            net_price=commodity.net_price,
            gross_price=commodity.gross_price,
            tax=commodity.tax,
            is_full_package_expenditure=True,
            document=wz_document,
        )
        wz_rows.append(wz_row)

    WarehouseDocumentRow.objects.using(db_name).bulk_create(wz_rows, 1000)
    print("--- WarehouseDocumentRows created ---")
    print("--- %s seconds ---" % (time.time() - start_time))


def noop(app, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0074_warehousedocument_hidden'),
    ]

    operations = [
        migrations.RunPython(
            migrate_old_receipts_transactions,
            reverse_code=noop,
        ),
    ]
