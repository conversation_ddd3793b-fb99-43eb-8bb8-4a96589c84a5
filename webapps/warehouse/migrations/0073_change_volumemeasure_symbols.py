# Generated by Django 2.0.13 on 2020-02-18 14:55

from django.db import migrations


def change_volume_measure_symbols(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    VolumeMeasure = apps.get_model('warehouse', 'VolumeMeasure')
    standard_measures = VolumeMeasure.objects.using(db_alias).filter(standard=True)
    standard_measures.filter(symbol='piece').update(symbol='pcs.')
    standard_measures.filter(symbol='ampoule').update(symbol='amp.')
    standard_measures.filter(symbol='pack').update(symbol='pkg.')


class Migration(migrations.Migration):
    dependencies = [
        ('warehouse', '0072_auto_20200218_0828'),
    ]

    operations = [
        migrations.RunPython(change_volume_measure_symbols, migrations.RunPython.noop),
    ]
