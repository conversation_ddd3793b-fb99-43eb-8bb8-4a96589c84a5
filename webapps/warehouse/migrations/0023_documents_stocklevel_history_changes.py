# Generated by Django 2.0.13 on 2019-07-26 08:13

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ('warehouse', '0022_merge_20190725_1307'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='commoditystocklevel',
            options={'get_latest_by': 'updated'},
        ),
        migrations.AddField(
            model_name='commoditystocklevel',
            name='created',
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now, verbose_name='Created (UTC)'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='commoditystocklevel',
            name='deleted',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
        ),
        migrations.AddField(
            model_name='commoditystocklevel',
            name='history_change',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=[]),
        ),
        migrations.AddField(
            model_name='commoditystocklevel',
            name='updated',
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name='Updated (UTC)'),
        ),
        migrations.AddField(
            model_name='warehousedocument',
            name='history_change',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=[]),
        ),
        migrations.AddField(
            model_name='warehousedocumentrow',
            name='history_change',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=[]),
        ),
    ]
