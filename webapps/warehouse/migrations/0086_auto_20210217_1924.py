# Generated by Django 3.1.2 on 2021-02-17 19:24

from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields
import webapps.warehouse.models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0085_commoditycategory_parent'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='commodity',
            managers=[
                ('objects', webapps.warehouse.models.CommodityManager()),
            ],
        ),
        migrations.AddField(
            model_name='commodity',
            name='catalog_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='commodity',
            name='extra_categories',
            field=models.ManyToManyField(
                blank=True, related_name='commodities_extra', to='warehouse.CommodityCategory'
            ),
        ),
        migrations.AddField(
            model_name='warehouse',
            name='country',
            field=django_countries.fields.CountryField(blank=True, max_length=2, null=True),
        ),
    ]
