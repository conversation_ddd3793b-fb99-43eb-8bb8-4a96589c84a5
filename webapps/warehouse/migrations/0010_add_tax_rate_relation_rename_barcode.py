# Generated by Django 2.0.13 on 2019-07-03 11:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0009_barcode_type_commodity_retail'),
    ]

    operations = [
        migrations.RenameField(
            model_name='commodity',
            old_name='barcode',
            new_name='barcodes',
        ),
        migrations.RemoveField(
            model_name='commodity',
            name='tax_rate',
        ),
        migrations.AddField(
            model_name='commodity',
            name='tax_rate',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='pos.TaxRate',
            ),
        ),
    ]
