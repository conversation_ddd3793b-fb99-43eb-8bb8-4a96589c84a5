# Generated by Django 2.0.13 on 2019-08-01 09:22
import datetime

from django.db import migrations, models
import django.db.models.deletion


def remove_old_documents(apps, schema_editor):
    """Existing documents will receive obligatory pointer to their base object.
    There is no simple way to create this base objects and inject their pks
    to subclass objects. It's much simpler to remove any subclassing
    documents that might exist before applying this migration.

    This operation should be regarded as one-time-hack as data models and
    database schema changed during the development and won't be necessary
    when migrating from clean "no-warehouse" state. It is therefore marked
    as "elidable" and intended to be removed upon the final squash of all
    warehouse migrations.
    """
    db_name = schema_editor.connection.alias
    WarehouseDocument = apps.get_model('warehouse', 'WarehouseDocument')
    Supply = apps.get_model('warehouse', 'Supply')
    WarehouseDocument.objects.using(db_name).delete()
    Supply.objects.using(db_name).delete()


def noop(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ('warehouse', '0029_documentrow_remove_unit'),
    ]

    operations = [
        migrations.RunPython(
            remove_old_documents,
            elidable=True,
            reverse_code=noop,
        ),
        migrations.CreateModel(
            name='BaseWarehouseDocument',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'manually_assigned_number',
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=50,
                        null=True,
                    ),
                ),
                ('issue_date', models.DateField(default=datetime.date.today)),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('WZ', 'Stock issue confirmation'),
                            ('RW', 'Internal expenditure'),
                            ('MM', 'Inter stock transfer'),
                            ('PZ', 'External stock reception'),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    'issuing_staffer',
                    models.ForeignKey(
                        to='business.Resource',
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='issued_documents',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.AlterModelOptions(
            name='supply',
            options={'get_latest_by': 'updated'},
        ),
        migrations.AlterModelOptions(
            name='warehousedocument',
            options={'get_latest_by': 'updated'},
        ),
        migrations.RemoveField(
            model_name='supply',
            name='created',
        ),
        migrations.RemoveField(
            model_name='supply',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='supply',
            name='id',
        ),
        migrations.RemoveField(
            model_name='supply',
            name='updated',
        ),
        migrations.RemoveField(
            model_name='supply',
            name='issuing_staffer',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='issuing_staffer',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='created',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='id',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='manually_assigned_number',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='updated',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='issue_date',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='type',
        ),
        migrations.AddField(
            model_name='supply',
            name='basewarehousedocument_ptr',
            field=models.OneToOneField(
                auto_created=True,
                on_delete=django.db.models.deletion.CASCADE,
                parent_link=True,
                primary_key=True,
                serialize=False,
                to='warehouse.BaseWarehouseDocument',
            ),
        ),
        migrations.AddField(
            model_name='warehousedocument',
            name='basewarehousedocument_ptr',
            field=models.OneToOneField(
                auto_created=True,
                on_delete=django.db.models.deletion.CASCADE,
                parent_link=True,
                primary_key=True,
                serialize=False,
                to='warehouse.BaseWarehouseDocument',
            ),
        ),
    ]
