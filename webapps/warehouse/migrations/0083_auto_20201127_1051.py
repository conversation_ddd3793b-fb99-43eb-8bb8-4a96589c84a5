# Generated by Django 3.1.2 on 2020-11-27 10:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0082_merge_20201124_1415'),
    ]

    operations = [
        migrations.AlterField(
            model_name='barcode',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='commoditycategory',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='commoditystocklevel',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='stocktakingdocument',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='stocktakingdocumentrow',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='supply',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='supplyrow',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='volumemeasure',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='warehousedocument',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='warehousedocumentrow',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='warehouseformula',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='warehouseformularow',
            name='history_change',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
    ]
