# Generated by Django 2.0.13 on 2019-06-27 14:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0003_add_on_delete'),
    ]

    operations = [
        migrations.AddField(
            model_name='barcode',
            name='business',
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to='business.Business',
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='barcode',
            name='code',
            field=models.CharField(
                blank=True,
                default='',
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='volumemeasure',
            name='businesses',
            field=models.ManyToManyField(
                blank=True,
                related_name='measures',
                to='business.Business',
            ),
        ),
        migrations.AddField(
            model_name='volumemeasure',
            name='label',
            field=models.CharField(
                blank=True,
                default='',
                max_length=50,
                null=True,
            ),
        ),
        migrations.RemoveField(
            model_name='commodity',
            name='symbol',
        ),
        migrations.AddField(
            model_name='commodity',
            name='symbol',
            field=models.CharField(
                blank=True,
                default='',
                max_length=50,
                null=True,
            ),
        ),
        migrations.DeleteModel(
            name='Symbol',
        ),
    ]
