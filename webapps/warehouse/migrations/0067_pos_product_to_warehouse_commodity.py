from django.db import migrations
from django.db.models.expressions import (
    F,
    OuterRef,
    Subquery,
)


def migrate_pos_category_order(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    CommodityCategory = apps.get_model('warehouse', 'CommodityCategory')
    ProductCategory = apps.get_model('pos', 'ProductCategory')

    product_categories_ids = (
        ProductCategory.objects.using(db_alias)
        .all()
        .values_list(
            'id',
            flat=True,
        )
    )
    categories_to_migrate = CommodityCategory.objects.using(db_alias).filter(
        id__in=product_categories_ids,
    )
    categories_to_migrate.annotate(
        pos_order=Subquery(
            ProductCategory.objects.using(db_alias).filter(id=OuterRef('id')).values('order')[:1]
        ),
    ).update(order=F('pos_order'))


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ('warehouse', '0066_commodity_categories_reorder'),
        ('warehouse', '0062_remove_commoditycategory_parent'),
    ]

    operations = [
        migrations.RunPython(
            migrate_pos_category_order,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
