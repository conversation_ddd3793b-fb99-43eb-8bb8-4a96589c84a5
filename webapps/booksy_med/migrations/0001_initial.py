# Generated by Django 4.2.16 on 2024-10-10 11:56

from django.db import migrations, models
import django.db.models.deletion
import webapps.billing.models.managers


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user', '0068_passwordresettoken'),
    ]

    operations = [
        migrations.CreateModel(
            name='BooksyMedAgreement',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('medical_data_consent', models.BooleanField(default=False)),
                (
                    'user',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='booksy_med_agreement',
                        to='user.user',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Booksy Med Agreement',
                'verbose_name_plural': 'Booksy Med Agreements',
            },
            managers=[
                ('objects', webapps.billing.models.managers.AutoUpdateAndAutoAddHistoryManager()),
            ],
        ),
        migrations.CreateModel(
            name='BooksyMedAgreementHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='history',
                        to='booksy_med.booksymedagreement',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]
