import typing as t
import uuid

from django.conf import settings


def is_medical_consent_required(business: 'Business', user: t.Union['User', None] = None) -> bool:
    return business.show_medical_consents and (
        user is None
        or (
            not user.booksy_med_agreement_exist
            or not user.booksy_med_agreement.medical_data_consent
        )
    )


def is_email_from_booksy_med_import(email: str | None) -> bool:
    """
    Checks if the email belongs to an imported customer from existing providers in ZnajdzGabinet.

    The email must start with the prefix defined in `BM_CUSTOMER_IMPORT_PREFIX`
    and end with "booksy.com".

    Args:
        email (str | None): The email address to check.

    Returns:
        bool: True if the email matches the required format, False otherwise.
    """
    if email is None:
        return False

    email_lower = email.lower()

    return email_lower.startswith(settings.BM_CUSTOMER_IMPORT_PREFIX) and email_lower.endswith(
        settings.BM_CUSTOMER_IMPORT_DOMAIN
    )


def generate_booksy_med_import_email():
    import_prefix = settings.BM_CUSTOMER_IMPORT_PREFIX
    import_domain = settings.BM_CUSTOMER_IMPORT_DOMAIN
    unique_id = uuid.uuid4()

    return f'{import_prefix}-{unique_id}@{import_domain}'
