from django.conf import settings
from django.utils.translation import gettext_lazy as _


class AgreementWrapper:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_title(self):
        return _('Medical data')

    def get_description(self):
        return _(
            'I agree for Booksy International sp. z o.o. to process my medical '
            'data to the extent necessary for me to book a service (in particular, '
            'the name and type of service) and to make it available to the entities '
            'with which I book medical appointments.'
        )

    def get_data_processing_info(self):
        return _('Information on data processing')

    def get_learn_more(self):
        return _('Learn more')

    def get_more_information(self):
        return _(
            'Information about your booked medical appointment or the frequency with which '
            'you use medical services is information regarding your state of health, '
            'therefore Booksy needs to obtain your consent to be able to legally process '
            'this information about you. <PERSON>y uses this data solely to enable you to book '
            'the services you have selected. This consent does not authorize <PERSON>y to '
            'access your medical records. You can withdraw your consent at any time, but '
            'this will result in the inability for you to book a medical service, and it '
            'does not affect the processing carried out before its withdrawal.'
        )

    def get_withdraw_consent(self):
        return _('(You may withdraw the above consent at any time)')


class BooksyMedDataConsent(AgreementWrapper):
    pass


# MAPPINGS
BM_AGREEMENTS = {
    settings.BM_MEDICAL_DATA_CONSENT: BooksyMedDataConsent(),
}
