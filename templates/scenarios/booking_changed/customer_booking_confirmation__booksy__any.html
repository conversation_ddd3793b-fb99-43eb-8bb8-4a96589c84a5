{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
{% if booking_info.is_booksy_pay_payment_window_open %}
    {{ _("Booking confirmed! Pay now with Booksy Pay.") }}
{% else %}
    {{ _("Your booking has been confirmed") }}
{% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {% if booking_info.is_booksy_pay_payment_window_open %}
            {{ _("You're booked with {business_name}{additional_booking_info}. Pay now with Booksy Pay using BLIK, card, Apple Pay or Google Pay and get it all set!").format(business_name=booking_info.business_name, additional_booking_info=booking_info.additional_booking_info) }}
        {% else %}
            {{ _("{business_name} has confirmed your booking{additional_booking_info}").format(business_name=booking_info.business_name, additional_booking_info=booking_info.additional_booking_info) }}
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% if booking_info.is_booksy_pay_payment_window_open and booking_info.booksy_pay_cashback_info %}
        {% call macros.paragraph() %}
            {{ booking_info.booksy_pay_cashback_info }} <a href="{{ booking_info.booksy_pay_cashback_policy_url }}">{{booking_info.booksy_pay_cashback_policy_text }}</a>
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        {% if booking_info.is_booksy_pay_payment_window_open %}
            {% call macros.link(url=booking_info.booking_marketplace_url) %}
                {{ _("You can reschedule or cancel the booking in the My bookings tab in your Booksy app.") }}
            {% endcall %}
        {% else %}
            {{ _("To change or cancel a booking, log into:") }}
            {% call macros.link(url=booking_info.booking_marketplace_url) %}
                {{ _("booksy.com") }}
            {% endcall %}
            {{ _('and select the "My bookings" bookmark.') }}
        {% endif %}
    {% endcall %}

    {% if show_disclosure_obligation_agreement %}
        {% call macros.paragraph() %}
            {{ disclosure_obligation_agreement_description }}
            {% call macros.link(url=disclosure_obligation_agreement_url) %}
                {{ disclosure_obligation_agreement_url_description }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description2 }}
            {% call macros.link(url=disclosure_obligation_agreement_url2) %}
                {{ disclosure_obligation_agreement_url_description2 }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description3 }}
        {% endcall %}
    {% endif %}
{% endblock email_content %}

{%if API_COUNTRY == 'pl'%}
    {% block email_rodo %}

    Potwierdzamy, że umówiłeś wizytę u Partnera [{{booking_info.business_name}}, {{booking_info.business_address}}]. Partner i Booksy International sp. z o. o. są współadministratorami Twoich danych osobowych. Będziemy przetwarzać Twoje dane w celu korzystania z serwisu Booksy i korzystania z usług Partnera, na podstawie umowy (art. 6 ust. 1 lit. b RODO), Twojej zgody (art. 6 ust. 1 lita RODO), prawnie uzasadnionego interesu (art. 6 ust. 1 lit. f RODO) oraz przepisów prawa (art. 6 ust. 1 lit. c RODO). Przysługuje Ci prawo: żądania dostępu do danych, otrzymywania ich kopii; sprostowania (poprawiania); usunięcia; ograniczenia przetwarzania; przenoszenia; wniesienia skargi do organu nadzorczego; wycofania zgody na przetwarzanie w dowolnym momencie (cofnięcie zgody nie wpływa na zgodność z prawem przetwarzania, którego dokonano przed jej wycofaniem) lub wniesienia sprzeciwu wobec przetwarzania danych. Więcej informacji znajdziesz <a href="https://booksy.com/pl-pl/privacy.html">tutaj</a>.

    {% endblock email_rodo %}
{% endif %}

